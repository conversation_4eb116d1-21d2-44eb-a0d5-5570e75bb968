# Mobile Month View Guide

## Cải tiến Month View cho Mobile

### Tính năng mới
Đã cải tiến month view để tối ưu cho mobile devices với screen width < 667px:

#### 1. **Responsive Design**
- **Desktop/Tablet (≥667px)**: Hi<PERSON>n thị đầy đủ thông tin môn học như trước
- **Mobile (<667px)**: Ẩn thông tin chi tiết, thay thế bằng hình tròn nhỏ

#### 2. **Mobile View Features**
- **Hình tròn nhỏ**: Mỗi môn học được biểu diễn bằng một chấm tròn nhỏ (2x2px) màu primary
- **Tối đa 6 chấm**: Hi<PERSON>n thị tối đa 6 chấm, nếu có nhiều hơn sẽ hiển thị "+X" 
- **Touch-friendly**: <PERSON><PERSON><PERSON> bộ cell có thể nhấn được với cursor pointer
- **Visual feedback**: Hover effect khi nhấn vào cell

#### 3. **Bottom Sheet Details**
Khi user nhấn vào cell có môn học trên mobile:
- **Bottom Sheet**: Hiển thị từ dưới lên với animation mượt mà
- **Chi tiết đầy đủ**: Tên môn, thời gian, phòng học, giảng viên, ghi chú
- **Responsive title**: Hiển thị ngày được chọn (ví dụ: "Thứ Hai, 4 tháng 12")
- **Easy close**: Có thể đóng bằng backdrop hoặc nút X

### Cách sử dụng

#### Trên Desktop/Tablet
- Month view hoạt động như bình thường
- Hover vào môn học để xem popover với chi tiết
- Click để xem thông tin đầy đủ

#### Trên Mobile
1. Chuyển sang Month view
2. Nhìn thấy các chấm tròn nhỏ thay vì text môn học
3. Nhấn vào cell có chấm để xem chi tiết
4. Bottom sheet sẽ hiển thị với thông tin đầy đủ
5. Vuốt xuống hoặc nhấn X để đóng

### Technical Implementation

#### Responsive Breakpoint
```css
/* Desktop/Tablet - Show full details */
.min-[667px]:block

/* Mobile - Show dots only */
.min-[667px]:hidden
```

#### Mobile Dots
```jsx
{day.subjects.slice(0, 6).map((_, index) => (
  <div className="w-2 h-2 rounded-full bg-primary" />
))}
```

#### Bottom Sheet Integration
- Sử dụng component `BottomSheet` đã tạo
- State management với `selectedDayData` và `showDayDetails`
- Responsive window size detection với `useEffect`

### Benefits

1. **Better Mobile UX**: Không bị cluttered với text nhỏ khó đọc
2. **Touch-friendly**: Dễ dàng nhấn vào cell
3. **Information Density**: Vẫn thấy được ngày nào có lịch học
4. **Detailed View**: Chi tiết đầy đủ khi cần thiết
5. **Smooth Animation**: Trải nghiệm mượt mà với bottom sheet

### Testing

#### Desktop Testing
1. Mở browser ở desktop size (>667px)
2. Chuyển sang Month view
3. Verify: Hiển thị đầy đủ thông tin môn học
4. Hover để xem popover

#### Mobile Testing
1. Resize browser xuống <667px hoặc dùng mobile device
2. Chuyển sang Month view  
3. Verify: Chỉ hiển thị chấm tròn nhỏ
4. Nhấn vào cell có chấm
5. Verify: Bottom sheet hiển thị với chi tiết đầy đủ
6. Test đóng bottom sheet

### Future Enhancements
- Swipe gestures để navigate giữa các tháng
- Color coding cho các loại môn học khác nhau
- Quick actions trong bottom sheet (thêm vào calendar, set reminder)
- Haptic feedback trên mobile devices
